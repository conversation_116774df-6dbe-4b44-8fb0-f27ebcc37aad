# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Virtual environments
venv/
env/
ENV/
.venv/
.env/

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Streamlit
.streamlit/secrets.toml

# Model files (will be downloaded automatically)
*.pth
*.pt
*.ckpt
models/
sam_vit_b_01ec64.pth

# Temporary files
*.tmp
*.temp
temp/
tmp/

# Logs
*.log
logs/

# Test files
test_*.py
*_test.py