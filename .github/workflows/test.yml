# This CI pipeline ensures that:

# All Python versions you support can run your app.
# System dependencies are correctly installed.
# Critical files are syntactically correct.
# Core libraries and custom modules are importable.
# A custom deployment verification script is successfully executed. 

name: Test Drishya App

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

jobs:
  test:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        python-version: [3.8, 3.9, "3.10", "3.11"]

    steps:
    - uses: actions/checkout@v3 #Clones GitHub repo into the runner

    
    - name: Set up Python ${{ matrix.python-version }}
      uses: actions/setup-python@v3
      with:
        python-version: ${{ matrix.python-version }}
    
    - name: Install system dependencies
      run: |
        sudo apt-get update
        sudo apt-get install -y libgl1-mesa-glx libglib2.0-0 libsm6 libxrender1 libxext6
    
    - name: Install Python dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements.txt
    
    - name: Run deployment verification
      run: |
        python verify_deployment.py
    
    - name: Test app import
      run: |
        python -c "
        import streamlit as st
        import torch
        import numpy as np
        import cv2
        from segment_anything import sam_model_registry
        print('✅ All imports successful')
        "
    
    - name: Check Streamlit syntax
      run: |
        python -m py_compile sam-roboflow.py
        echo "✅ Python syntax check passed"
